// Generated model class for StoreOut
// This file is auto-generated. Do not edit manually.

/// Simplified store schema for order responses
class StoreOut {
  final int id;

  final String name;

  final String address;

  const StoreOut({
    required this.id,
    required this.name,
    required this.address,
  });

  factory StoreOut.fromJson(Map<String, dynamic> json) {
    return StoreOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
    };
  }

  @override
  String toString() {
    return 'StoreOut(id: $id, name: $name, address: $address)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ address.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreOut) return false;
    return id == other.id && name == other.name && address == other.address;
  }

}

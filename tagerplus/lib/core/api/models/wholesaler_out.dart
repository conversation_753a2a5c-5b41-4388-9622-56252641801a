// Generated model class for WholesalerOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for wholesaler output
class WholesalerOut {
  final int id;

  final String title;

  final String description;

  final String phone;

  final String address;

  final CustomUserOut user;

  final String createdAt;

  final String updatedAt;

  const WholesalerOut({
    required this.id,
    required this.title,
    required this.description,
    required this.phone,
    required this.address,
    required this.user,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WholesalerOut.fromJson(Map<String, dynamic> json) {
    return WholesalerOut(
      id: json['id'] as int? ?? 0,
      title: json['title']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      user: CustomUserOut.fromJson(json['user'] as Map<String, dynamic>? ?? {}),
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'phone': phone,
      'address': address,
      'user': user.toJson(),
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'WholesalerOut(id: $id, title: $title, description: $description, phone: $phone, address: $address, user: $user, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ description.hashCode ^ phone.hashCode ^ address.hashCode ^ user.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerOut) return false;
    return id == other.id && title == other.title && description == other.description && phone == other.phone && address == other.address && user == other.user && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}

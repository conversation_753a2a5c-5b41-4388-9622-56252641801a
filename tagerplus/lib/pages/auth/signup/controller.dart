import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/api/api.dart';
import 'package:tagerplus/pages/store/create/create.dart';

class SignupController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  final RxBool isPasswordHidden = true.obs;
  final RxBool isConfirmPasswordHidden = true.obs;
  final RxBool isLoading = false.obs;
  final RxBool agreeToTerms = false.obs;

  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordHidden.value = !isConfirmPasswordHidden.value;
  }

  void toggleTermsAgreement() {
    agreeToTerms.value = !agreeToTerms.value;
  }

  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'please_enter_name'.tr;
    }
    if (value.trim().length < 2) {
      return 'name_too_short'.tr;
    }
    return null;
  }

  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'please_enter_phone'.tr;
    }
    if (value.trim().length < 8) {
      return 'enter_valid_phone'.tr;
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'please_enter_password'.tr;
    }
    if (value.length < 6) {
      return 'password_min_length'.tr;
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'please_confirm_password'.tr;
    }
    if (value != passwordController.text) {
      return 'passwords_dont_match'.tr;
    }
    return null;
  }

  Future<void> signup() async {
    if (isLoading.value) return;

    if (!agreeToTerms.value) {
      Get.snackbar('error'.tr, 'please_agree_terms'.tr);
      return;
    }

    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    isLoading.value = true;
    try {
      final authService = AuthService();
      final registerRequest = RegisterRequest(
        name: nameController.text.trim(),
        phone: phoneNumberController.text.trim(),
        password: passwordController.text,
      );

      final registerResponse = await authService.signup(registerRequest);

      if (registerResponse.success) {
        Get.snackbar('success'.tr, 'account_created_successfully'.tr);

        // Navigate to store creation since new users don't have stores
        Get.offAll(() => const StoreCreatePage());
      } else {
        Get.snackbar('error'.tr, 'signup_failed'.tr);
      }
    } on Exception catch (e) {
      String errorMessage = 'signup_failed'.tr;

      if (e.toString().contains('phone')) {
        errorMessage = 'phone_already_exists'.tr;
      } else if (e.toString().contains('password')) {
        errorMessage = 'weak_password'.tr;
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'network_error'.tr;
      } else if (e.toString().contains('Server')) {
        errorMessage = 'server_error'.tr;
      }

      if (kDebugMode) {
        print('Signup error: $e');
      }

      Get.snackbar('error'.tr, errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected signup error: $e');
      }
      Get.snackbar('error'.tr, 'signup_failed'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneNumberController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}

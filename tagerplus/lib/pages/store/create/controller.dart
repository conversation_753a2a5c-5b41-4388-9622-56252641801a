import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/api/api.dart';

class StoreCreateController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Text controllers
  final TextEditingController storeNameController = TextEditingController();
  final TextEditingController storeDescriptionController =
      TextEditingController();
  final TextEditingController storeAddressController = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingRegions = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<RegionWithHierarchyOut> _regions =
      <RegionWithHierarchyOut>[].obs;
  final Rx<RegionWithHierarchyOut?> _selectedRegion =
      Rx<RegionWithHierarchyOut?>(null);

  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  bool get isLoadingRegions => _isLoadingRegions.value;
  String get errorMessage => _errorMessage.value;
  List<RegionWithHierarchyOut> get regions => _regions;
  RegionWithHierarchyOut? get selectedRegion => _selectedRegion.value;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;
  set isLoadingRegions(bool value) => _isLoadingRegions.value = value;
  set selectedRegion(RegionWithHierarchyOut? value) =>
      _selectedRegion.value = value;

  @override
  void onInit() {
    super.onInit();
    loadRegions();
  }

  /// Load available regions
  Future<void> loadRegions() async {
    if (isLoadingRegions) return;

    isLoadingRegions = true;
    try {
      final regionsService = RegionsService();
      final regionsList = await regionsService.listRegionsWithHierarchy();
      _regions.value = regionsList;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading regions: $e');
      }
      Get.snackbar('error'.tr, 'network_error'.tr);
    } finally {
      isLoadingRegions = false;
    }
  }

  /// Validation methods
  String? validateStoreName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'please_enter_store_name'.tr;
    }
    if (value.trim().length < 2) {
      return 'name_too_short'.tr;
    }
    return null;
  }

  String? validateStoreAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'please_enter_store_address'.tr;
    }
    if (value.trim().length < 5) {
      return 'address_too_short'.tr;
    }
    return null;
  }

  String? validateRegion(RegionWithHierarchyOut? value) {
    if (value == null) {
      return 'please_select_region'.tr;
    }
    return null;
  }

  /// Create store
  Future<void> createStore() async {
    if (isLoading) return;

    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    if (selectedRegion == null) {
      Get.snackbar('error'.tr, 'please_select_region'.tr);
      return;
    }

    isLoading = true;
    try {
      // Map region to appropriate field based on type
      int? cityId, stateId, countryId;
      switch (selectedRegion!.type.toLowerCase()) {
        case 'city':
          cityId = selectedRegion!.id;
          stateId = selectedRegion!.parentId;
          break;
        case 'state':
          stateId = selectedRegion!.id;
          countryId = selectedRegion!.parentId;
          break;
        case 'country':
          countryId = selectedRegion!.id;
          break;
        default:
          // Default to city if type is unknown
          cityId = selectedRegion!.id;
      }

      final storesService = StoresService();
      final authService = AuthService();
      final currentUser = await authService.getCurrentUser();

      final storeRequest = StoreIn(
        name: storeNameController.text.trim(),
        description: storeDescriptionController.text.trim().isEmpty
            ? ''
            : storeDescriptionController.text.trim(),
        address: storeAddressController.text.trim(),
        cityId: cityId,
        stateId: stateId,
        countryId: countryId,
        ownerId: currentUser.id,
      );

      await storesService.createStore(storeRequest);

      Get.snackbar('success'.tr, 'store_created_successfully'.tr);

      // Navigate to home page
      Get.offAll(
        () => const Scaffold(
          body: Center(child: Text('Home Page - Coming Soon')),
        ),
      );
    } on Exception catch (e) {
      String errorMessage = 'store_creation_failed'.tr;

      if (e.toString().contains('validation')) {
        errorMessage = e.toString();
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'network_error'.tr;
      } else if (e.toString().contains('Server')) {
        errorMessage = 'server_error'.tr;
      }

      if (kDebugMode) {
        print('Store creation error: $e');
      }

      Get.snackbar('error'.tr, errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected store creation error: $e');
      }
      Get.snackbar('error'.tr, 'store_creation_failed'.tr);
    } finally {
      isLoading = false;
    }
  }

  /// Skip store creation for now
  void skipStoreCreation() {
    Get.offAll(
      () =>
          const Scaffold(body: Center(child: Text('Home Page - Coming Soon'))),
    );
  }

  /// Cleanup
  @override
  void onClose() {
    storeNameController.dispose();
    storeDescriptionController.dispose();
    storeAddressController.dispose();
    super.onClose();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/pages/store/create/controller.dart';
import 'package:tagerplus/api/api.dart';

class StoreFormWidget extends StatelessWidget {
  final StoreCreateController controller;
  final ThemeData theme;

  const StoreFormWidget({
    super.key,
    required this.controller,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Store Name Field
          TextFormField(
            controller: controller.storeNameController,
            textInputAction: TextInputAction.next,
            validator: controller.validateStoreName,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: _buildInputDecoration(
              labelText: 'store_name'.tr,
              prefixIcon: Icons.store_rounded,
            ),
          ),
          const SizedBox(height: 16),

          // Store Description Field (Optional)
          TextFormField(
            controller: controller.storeDescriptionController,
            textInputAction: TextInputAction.next,
            maxLines: 3,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: _buildInputDecoration(
              labelText: 'store_description'.tr,
              prefixIcon: Icons.description_rounded,
              hintText: 'store_description_hint'.tr,
            ),
          ),
          const SizedBox(height: 16),

          // Store Address Field
          TextFormField(
            controller: controller.storeAddressController,
            textInputAction: TextInputAction.next,
            validator: controller.validateStoreAddress,
            maxLines: 2,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: _buildInputDecoration(
              labelText: 'store_address'.tr,
              prefixIcon: Icons.location_on_rounded,
            ),
          ),
          const SizedBox(height: 16),

          // Region Dropdown
          Obx(
            () => DropdownButtonFormField<RegionWithHierarchyOut>(
              initialValue: controller.selectedRegion,
              onChanged: (RegionWithHierarchyOut? newValue) {
                controller.selectedRegion = newValue;
              },
              validator: (value) => controller.validateRegion(value),
              decoration: _buildInputDecoration(
                labelText: 'select_region'.tr,
                prefixIcon: Icons.map_rounded,
              ),
              items: controller.regions
                  .map<DropdownMenuItem<RegionWithHierarchyOut>>((
                    RegionWithHierarchyOut region,
                  ) {
                    return DropdownMenuItem<RegionWithHierarchyOut>(
                      value: region,
                      child: Text(
                        region.hierarchicalName,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.black87,
                        ),
                      ),
                    );
                  })
                  .toList(),
              hint: controller.isLoadingRegions
                  ? Row(
                      children: [
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'loading'.tr,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      'select_region'.tr,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
              dropdownColor: Colors.white,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  InputDecoration _buildInputDecoration({
    required String labelText,
    required IconData prefixIcon,
    String? hintText,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: Icon(prefixIcon, color: AppColors.primary),
      filled: true,
      fillColor: Colors.grey.shade50,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: AppColors.primary, width: 2.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: const BorderSide(color: Colors.red, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: const BorderSide(color: Colors.red, width: 2.5),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
    );
  }
}

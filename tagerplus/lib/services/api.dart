import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/client/api_client.dart';

class ApiService extends GetxService {
  late final ApiClient _apiClient;
  late final Dio _dio;

  static ApiService get to => Get.find();

  ApiClient get apiClient => _apiClient;

  ApiService init() {
    _dio = Dio();
    _apiClient = ApiClient(baseUrl: '10.0.2.2:8000', dio: _dio);
    return this;
  }
}
